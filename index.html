<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品智算引擎 v0.3 - 智能库存调拨</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@100;200;300;400;500;600;700;800;900&family=SF+Pro+Text:wght@100;200;300;400;500;600;700;800;900&display=swap');

        :root {
            /* 主色调 - 清新柔和的渐变系统 */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --premium-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 50%, #d299c2 100%);
            --hero-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --success-gradient: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
            --warning-gradient: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            --info-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);

            /* 玻璃态效果 */
            --glass-bg: rgba(255, 255, 255, 0.9);
            --glass-bg-light: rgba(255, 255, 255, 0.7);
            --glass-border: rgba(255, 255, 255, 0.4);
            --glass-border-strong: rgba(255, 255, 255, 0.6);

            /* 阴影系统 */
            --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.08);
            --shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.12);
            --shadow-strong: 0 15px 40px rgba(0, 0, 0, 0.15);
            --shadow-glow: 0 0 30px rgba(102, 126, 234, 0.3);

            /* 文字颜色 */
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --text-muted: #a0aec0;

            /* 强调色系统 */
            --accent-blue: #4299e1;
            --accent-purple: #9f7aea;
            --accent-pink: #ed64a6;
            --accent-green: #48bb78;
            --accent-orange: #ed8936;
            --accent-teal: #38b2ac;

            /* 背景色系统 */
            --bg-primary: #f7fafc;
            --bg-secondary: #edf2f7;
            --bg-card: rgba(255, 255, 255, 0.95);
        }

        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body {
            font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 20%, #a8edea 60%, #fed6e3 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .sf-pro-display {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            font-weight: 600;
            letter-spacing: -0.02em;
        }

        .premium-gradient {
            background: var(--premium-gradient);
        }

        .gradient-bg {
            background: var(--primary-gradient);
        }

        .glass-morphism {
            background: var(--glass-bg);
            backdrop-filter: blur(30px);
            -webkit-backdrop-filter: blur(30px);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-soft);
            position: relative;
        }

        .glass-morphism::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
            border-radius: inherit;
            pointer-events: none;
        }

        .premium-card {
            background: linear-gradient(145deg, var(--glass-bg), var(--glass-bg-light));
            backdrop-filter: blur(35px);
            -webkit-backdrop-filter: blur(35px);
            border: 1px solid var(--glass-border-strong);
            box-shadow: var(--shadow-medium);
            position: relative;
            overflow: hidden;
            border-radius: 24px;
        }

        .premium-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.9), transparent);
        }

        .premium-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent 50%, rgba(255, 255, 255, 0.05));
            pointer-events: none;
        }

        .card-shadow {
            box-shadow: var(--shadow-soft);
            backdrop-filter: blur(25px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .hover-lift {
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .hover-lift:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: var(--shadow-strong);
        }

        .premium-hover {
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .premium-hover:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-glow);
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .floating { animation: float 6s ease-in-out infinite; }

        .glass-effect {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
        }

        .nav-item {
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            position: relative;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 0;
            background: var(--accent-blue);
            border-radius: 2px;
            transition: height 0.2s ease;
        }

        .nav-item:hover {
            background: rgba(0, 122, 255, 0.08);
            transform: translateX(4px);
        }

        .nav-item.active {
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.15), rgba(88, 86, 214, 0.1));
            color: var(--accent-blue);
            font-weight: 600;
            transform: translateX(4px);
        }

        .nav-item.active::before {
            height: 20px;
        }

        .premium-button {
            background: var(--premium-gradient);
            border: none;
            border-radius: 18px;
            color: white;
            font-weight: 600;
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: var(--shadow-soft);
        }

        .premium-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s ease;
        }

        .premium-button:hover::before {
            left: 100%;
        }

        .premium-button:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: var(--shadow-glow);
        }

        .premium-button:active {
            transform: translateY(-1px) scale(0.98);
        }

        .apple-button {
            background: var(--info-gradient);
            border: none;
            border-radius: 16px;
            color: white;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-soft);
        }

        .apple-button:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 15px 35px rgba(116, 185, 255, 0.4);
        }

        .apple-button:active {
            transform: translateY(0) scale(0.98);
        }

        .premium-input {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 16px;
            transition: all 0.3s ease;
            background: var(--glass-bg);
            backdrop-filter: blur(15px);
            box-shadow: var(--shadow-soft);
        }

        .premium-input:focus {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 4px rgba(66, 153, 225, 0.15);
            outline: none;
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
        }

        .premium-input:hover {
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
        }

        .apple-input {
            border: 1.5px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            transition: all 0.2s ease;
            background: rgba(255, 255, 255, 0.95);
        }

        .apple-input:focus {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
            outline: none;
        }

        .status-indicator {
            position: relative;
        }

        .status-indicator::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
            border-radius: inherit;
            z-index: -1;
            opacity: 0.7;
            filter: blur(8px);
        }

        .metric-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .metric-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
            pointer-events: none;
        }

        .glow-effect {
            position: relative;
        }

        .glow-effect::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: inherit;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .glow-effect:hover::before {
            opacity: 0.3;
            filter: blur(10px);
        }

        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .premium-shadow {
            box-shadow:
                0 2px 8px rgba(0, 0, 0, 0.04),
                0 4px 16px rgba(0, 0, 0, 0.06),
                0 8px 32px rgba(0, 0, 0, 0.08);
        }

        /* Custom Slider Styles */
        .slider::-webkit-slider-thumb {
            appearance: none;
            height: 18px;
            width: 18px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            border: 2px solid white;
        }

        .slider::-webkit-slider-track {
            height: 6px;
            border-radius: 3px;
            background: #e2e8f0;
        }

        .slider::-webkit-slider-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        /* Blue slider for primary weight */
        .slider-blue::-webkit-slider-thumb {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            box-shadow: 0 4px 12px rgba(116, 185, 255, 0.4);
        }

        .slider-blue::-webkit-slider-thumb:hover {
            box-shadow: 0 6px 20px rgba(116, 185, 255, 0.6);
        }

        /* Purple slider for secondary weight */
        .slider-purple::-webkit-slider-thumb {
            background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
            box-shadow: 0 4px 12px rgba(159, 122, 234, 0.4);
        }

        .slider-purple::-webkit-slider-thumb:hover {
            box-shadow: 0 6px 20px rgba(159, 122, 234, 0.6);
        }

        /* Enhanced animations */
        @keyframes shimmer {
            0% { background-position: -200px 0; }
            100% { background-position: calc(200px + 100%) 0; }
        }

        .shimmer {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            background-size: 200px 100%;
            animation: shimmer 2s infinite;
        }

        /* Particle effect */
        .particle-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Advanced hover effects */
        .premium-hover-scale:hover {
            transform: scale(1.05) translateY(-2px);
        }

        .magnetic-hover {
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .magnetic-hover:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 30px 60px rgba(31, 38, 135, 0.3);
        }

        /* Enhanced animations */
        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .breathe {
            animation: breathe 4s ease-in-out infinite;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in-up {
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .fade-in-scale {
            animation: fadeInScale 0.5s ease-out;
        }

        /* Gradient text animation */
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .animated-gradient {
            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #a8edea);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="flex h-screen bg-gradient-to-br from-orange-50 via-pink-50 to-blue-50 relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 bg-gradient-to-br from-orange-50/40 via-pink-50/30 to-blue-50/40"></div>
        <div class="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-orange-300/15 to-pink-300/15 rounded-full blur-3xl floating"></div>
        <div class="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-br from-pink-300/15 to-blue-300/15 rounded-full blur-3xl floating" style="animation-delay: -3s;"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-blue-300/10 to-purple-300/10 rounded-full blur-2xl floating" style="animation-delay: -1.5s;"></div>

        <!-- Sidebar -->
        <div class="w-64 glass-morphism border-r border-white/30 flex flex-col relative z-10">
            <!-- Logo -->
            <div class="p-6 border-b border-white/20">
                <div class="flex items-center space-x-4">
                    <div class="w-14 h-14 premium-gradient rounded-3xl flex items-center justify-center shadow-lg glow-effect floating">
                        <i class="fas fa-brain text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-black sf-pro-display text-gradient">商品智算引擎</h1>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-600 font-semibold">v0.3</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-emerald-400 to-teal-500 text-white text-xs font-bold rounded-full shadow-lg">
                                PRO
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 p-4">
                <div class="space-y-1">
                    <a href="#" onclick="showSection('dashboard')" class="nav-item flex items-center space-x-3 px-4 py-3 text-gray-600 rounded-xl">
                        <i class="fas fa-chart-pie w-5 text-lg"></i>
                        <span class="font-medium">仪表盘</span>
                    </a>
                    <a href="#" onclick="showSection('allocation')" class="nav-item active flex items-center space-x-3 px-4 py-3 rounded-xl">
                        <i class="fas fa-exchange-alt w-5 text-lg"></i>
                        <span class="font-medium">智能调拨</span>
                    </a>
                    <a href="#" onclick="showSection('pricing')" class="nav-item flex items-center space-x-3 px-4 py-3 text-gray-600 rounded-xl">
                        <i class="fas fa-tags w-5 text-lg"></i>
                        <span class="font-medium">定价算法</span>
                    </a>
                    <a href="#" onclick="showSection('pricecontrol')" class="nav-item flex items-center space-x-3 px-4 py-3 text-gray-600 rounded-xl">
                        <i class="fas fa-sliders-h w-5 text-lg"></i>
                        <span class="font-medium">智能控价</span>
                    </a>
                    <a href="#" onclick="showSection('monitoring')" class="nav-item flex items-center space-x-3 px-4 py-3 text-gray-600 rounded-xl">
                        <i class="fas fa-search w-5 text-lg"></i>
                        <span class="font-medium">商品监控</span>
                    </a>
                    <a href="#" onclick="showSection('analytics')" class="nav-item flex items-center space-x-3 px-4 py-3 text-gray-600 rounded-xl">
                        <i class="fas fa-chart-bar w-5 text-lg"></i>
                        <span class="font-medium">数据分析</span>
                    </a>
                    <a href="#" onclick="showSection('aisettings')" class="nav-item flex items-center space-x-3 px-4 py-3 text-gray-600 rounded-xl">
                        <i class="fas fa-brain w-5 text-lg"></i>
                        <span class="font-medium">AI设置</span>
                    </a>
                </div>

                <!-- Quick Stats in Sidebar -->
                <div class="mt-8 premium-card rounded-3xl p-6 glow-effect fade-in-scale">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full pulse-animation"></div>
                        <h3 class="text-sm font-bold text-black sf-pro-display">实时概览</h3>
                    </div>
                    <div class="space-y-5">
                        <div class="flex justify-between items-center group hover:bg-white/30 rounded-xl p-2 transition-all duration-200">
                            <span class="text-xs text-gray-600 font-semibold">调拨任务</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-bold text-black">23</span>
                                <div class="w-2 h-2 bg-green-400 rounded-full pulse-animation"></div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center group hover:bg-white/30 rounded-xl p-2 transition-all duration-200">
                            <span class="text-xs text-gray-600 font-semibold">处理SKU</span>
                            <span class="text-sm font-bold text-black">1,247</span>
                        </div>
                        <div class="flex justify-between items-center group hover:bg-white/30 rounded-xl p-2 transition-all duration-200">
                            <span class="text-xs text-gray-600 font-semibold">成功率</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-bold text-emerald-600">94.2%</span>
                                <div class="w-10 h-2 bg-gray-200 rounded-full overflow-hidden">
                                    <div class="w-full h-full bg-gradient-to-r from-emerald-400 to-green-500 rounded-full transform scale-x-95 transition-all duration-1000"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- User Profile -->
            <div class="p-4 border-t border-gray-200/30">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center shadow-sm">
                        <i class="fas fa-user text-gray-600"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-semibold text-black">管理员</p>
                        <p class="text-xs text-gray-500"><EMAIL></p>
                    </div>
                    <button class="p-2 text-gray-400 hover:text-blue-500 rounded-xl transition-colors">
                        <i class="fas fa-bell text-sm"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col overflow-hidden relative z-10">
            <!-- Top Header -->
            <header class="glass-morphism border-b border-white/20 px-8 py-6 premium-shadow">
                <div class="flex justify-between items-center">
                    <div class="slide-in-up">
                        <h2 class="text-4xl font-bold text-black sf-pro-display mb-2 animated-gradient" id="pageTitle">智能库存调拨</h2>
                        <p class="text-sm text-gray-600 font-medium" id="pageDescription">基于AI算法的智能库存分配，解决多仓库间库存不均衡问题</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 px-3 py-2 bg-white/50 rounded-2xl backdrop-blur-sm">
                            <div class="w-2 h-2 bg-green-400 rounded-full pulse-animation"></div>
                            <span class="text-xs font-semibold text-gray-700">系统运行中</span>
                        </div>
                        <button class="premium-button px-6 py-3 text-white font-semibold">
                            <i class="fas fa-plus mr-2"></i>
                            新建调拨
                        </button>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="section-content hidden">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-2xl p-6 card-shadow hover-lift">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-500 mb-1">总池位数</p>
                                    <p class="text-2xl font-bold text-black">12</p>
                                    <p class="text-xs text-green-600 mt-1">↑ 2 新增</p>
                                </div>
                                <div class="w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-warehouse text-blue-500"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-2xl p-6 card-shadow hover-lift">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-500 mb-1">总库存量</p>
                                    <p class="text-2xl font-bold text-black">85.4万</p>
                                    <p class="text-xs text-blue-600 mt-1">↑ 3.2% 本月</p>
                                </div>
                                <div class="w-12 h-12 bg-green-50 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-boxes text-green-500"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-2xl p-6 card-shadow hover-lift">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-500 mb-1">调拨效率</p>
                                    <p class="text-2xl font-bold text-black">94.2%</p>
                                    <p class="text-xs text-green-600 mt-1">↑ 1.8% 本周</p>
                                </div>
                                <div class="w-12 h-12 bg-purple-50 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-chart-line text-purple-500"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-2xl p-6 card-shadow hover-lift">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-500 mb-1">节省成本</p>
                                    <p class="text-2xl font-bold text-black">¥28.5万</p>
                                    <p class="text-xs text-green-600 mt-1">本月累计</p>
                                </div>
                                <div class="w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-coins text-orange-500"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dashboard Charts -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white rounded-2xl p-6 card-shadow">
                            <h3 class="text-lg font-semibold text-black mb-4">调拨趋势分析</h3>
                            <div class="h-64 bg-gray-50 rounded-xl flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-chart-area text-4xl text-gray-300 mb-2"></i>
                                    <p class="text-gray-500">调拨趋势图表</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-2xl p-6 card-shadow">
                            <h3 class="text-lg font-semibold text-black mb-4">库存分布热力图</h3>
                            <div class="h-64 bg-gray-50 rounded-xl flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-map text-4xl text-gray-300 mb-2"></i>
                                    <p class="text-gray-500">库存热力图</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Allocation Section (Default Active) -->
                <div id="allocation-section" class="section-content">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div class="metric-card rounded-2xl p-5 premium-shadow hover-lift glow-effect">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-xs text-gray-500 mb-2 font-semibold uppercase tracking-wider">总池位数</p>
                                    <p class="text-3xl font-bold text-black sf-pro-display mb-1">12</p>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-sm text-emerald-600 font-bold">+2</span>
                                        <span class="text-xs text-gray-500">本月新增</span>
                                        <div class="w-1 h-1 bg-emerald-400 rounded-full pulse-animation"></div>
                                    </div>
                                </div>
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg status-indicator">
                                    <i class="fas fa-warehouse text-white text-lg"></i>
                                </div>
                            </div>
                        </div>
                        <div class="metric-card rounded-2xl p-5 premium-shadow hover-lift glow-effect">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-xs text-gray-500 mb-2 font-semibold uppercase tracking-wider">待调拨SKU</p>
                                    <p class="text-3xl font-bold text-black sf-pro-display mb-1">1,247</p>
                                    <div class="flex items-center space-x-1">
                                        <div class="w-1.5 h-1.5 bg-orange-400 rounded-full pulse-animation"></div>
                                        <span class="text-xs text-gray-500">实时更新</span>
                                    </div>
                                </div>
                                <div class="w-12 h-12 bg-gradient-to-br from-emerald-400 to-green-500 rounded-2xl flex items-center justify-center shadow-lg status-indicator">
                                    <i class="fas fa-boxes text-white text-lg"></i>
                                </div>
                            </div>
                        </div>
                        <div class="metric-card rounded-2xl p-5 premium-shadow hover-lift glow-effect">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-xs text-gray-500 mb-2 font-semibold uppercase tracking-wider">调拨效率</p>
                                    <p class="text-3xl font-bold text-black sf-pro-display mb-2">94.2%</p>
                                    <div class="w-full h-1.5 bg-gray-200 rounded-full overflow-hidden">
                                        <div class="w-full h-full bg-gradient-to-r from-purple-400 to-pink-500 rounded-full transform scale-x-95 transition-all duration-1000"></div>
                                    </div>
                                </div>
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg status-indicator">
                                    <i class="fas fa-chart-line text-white text-lg"></i>
                                </div>
                            </div>
                        </div>
                        <div class="metric-card rounded-2xl p-5 premium-shadow hover-lift glow-effect">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-xs text-gray-500 mb-2 font-semibold uppercase tracking-wider">节省成本</p>
                                    <p class="text-3xl font-bold text-black sf-pro-display mb-1">¥28.5万</p>
                                    <div class="flex items-center space-x-1">
                                        <span class="text-sm text-emerald-600 font-bold">↗ 15.3%</span>
                                        <span class="text-xs text-gray-500">较上月</span>
                                        <div class="w-1 h-1 bg-emerald-400 rounded-full pulse-animation"></div>
                                    </div>
                                </div>
                                <div class="w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg status-indicator">
                                    <i class="fas fa-coins text-white text-lg"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Dashboard -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Left Column -->
                        <div class="lg:col-span-2 space-y-5">
                            <!-- Algorithm Control Panel -->
                            <div class="premium-card rounded-2xl p-6 premium-shadow glow-effect">
                                <div class="flex items-center justify-between mb-6">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full pulse-animation"></div>
                                        <h3 class="text-xl font-bold text-black sf-pro-display">智能调拨控制台</h3>
                                    </div>
                                    <div class="status-indicator px-4 py-2 bg-gradient-to-r from-emerald-400 to-green-500 text-white rounded-xl text-sm font-bold shadow-lg">
                                        <i class="fas fa-circle text-white text-xs mr-2 pulse-animation"></i>
                                        算法运行中
                                    </div>
                                </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-bold text-gray-700 mb-3">产品类型</label>
                                    <select class="premium-input w-full p-3 text-sm font-medium">
                                        <option>固口类产品</option>
                                        <option>非固口类产品</option>
                                        <option>混合类型</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-bold text-gray-700 mb-3">分配策略</label>
                                    <select class="premium-input w-full p-3 text-sm font-medium">
                                        <option>智能权重分配</option>
                                        <option>按需求比例分配</option>
                                        <option>均衡优先分配</option>
                                    </select>
                                </div>
                        </div>
                                <div class="space-y-5">
                                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-4 border border-blue-100">
                                        <label class="block text-sm font-bold text-gray-700 mb-3">主权重系数</label>
                                        <div class="relative">
                                            <input type="range" min="0" max="100" value="75" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-blue">
                                            <div class="absolute top-0 left-0 h-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg pointer-events-none" style="width: 75%;"></div>
                                        </div>
                                        <div class="flex justify-between text-sm text-gray-600 mt-2 font-medium">
                                            <span>0%</span>
                                            <span class="px-2 py-1 bg-blue-500 text-white rounded-lg text-xs font-bold shadow-lg">75%</span>
                                            <span>100%</span>
                                        </div>
                                    </div>
                                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-4 border border-purple-100">
                                        <label class="block text-sm font-bold text-gray-700 mb-3">二级权重系数</label>
                                        <div class="relative">
                                            <input type="range" min="0" max="100" value="25" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-purple">
                                            <div class="absolute top-0 left-0 h-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg pointer-events-none" style="width: 25%;"></div>
                                        </div>
                                        <div class="flex justify-between text-sm text-gray-600 mt-2 font-medium">
                                            <span>0%</span>
                                            <span class="px-2 py-1 bg-purple-500 text-white rounded-lg text-xs font-bold shadow-lg">25%</span>
                                            <span>100%</span>
                                        </div>
                                    </div>
                                </div>
                    </div>

                                <div class="flex space-x-4 mt-6">
                                    <button class="premium-button flex-1 py-3 px-6 text-white font-bold text-base shadow-xl">
                                        <i class="fas fa-play mr-2"></i>
                                        开始智能调拨
                                    </button>
                                    <button class="px-6 py-3 border-2 border-white/50 text-gray-700 rounded-2xl font-bold hover:bg-white hover:shadow-xl transition-all duration-300 premium-hover backdrop-blur-sm">
                                        <i class="fas fa-download mr-2"></i>
                                        导出方案
                                    </button>
                                </div>
                </div>

                <!-- Data Import Section -->
                <div class="bg-white rounded-2xl p-5 card-shadow">
                    <h3 class="text-lg font-semibold text-black mb-4">数据导入</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                        <div class="border-2 border-dashed border-gray-200 rounded-xl p-4 text-center hover:border-blue-300 transition-colors cursor-pointer">
                            <i class="fas fa-file-excel text-green-500 text-2xl mb-2"></i>
                            <p class="text-sm font-medium text-black">池位分配需求表</p>
                            <p class="text-xs text-gray-500 mt-1">拖拽或点击上传</p>
                            <div class="mt-2">
                                <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">已上传</span>
                            </div>
                        </div>
                        <div class="border-2 border-dashed border-gray-200 rounded-xl p-4 text-center hover:border-blue-300 transition-colors cursor-pointer">
                            <i class="fas fa-database text-blue-500 text-2xl mb-2"></i>
                            <p class="text-sm font-medium text-black">共享池可用库存</p>
                            <p class="text-xs text-gray-500 mt-1">拖拽或点击上传</p>
                            <div class="mt-2">
                                <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">已上传</span>
                            </div>
                        </div>
                        <div class="border-2 border-dashed border-gray-200 rounded-xl p-4 text-center hover:border-blue-300 transition-colors cursor-pointer">
                            <i class="fas fa-warehouse text-purple-500 text-2xl mb-2"></i>
                            <p class="text-sm font-medium text-black">目标池位在库件数</p>
                            <p class="text-xs text-gray-500 mt-1">拖拽或点击上传</p>
                            <div class="mt-2">
                                <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs">待上传</span>
                            </div>
                        </div>
                    </div>

                    <!-- Data Summary -->
                    <div class="bg-gray-50 rounded-xl p-4">
                        <h4 class="text-sm font-medium text-black mb-3">数据概览</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                            <div>
                                <p class="text-lg font-bold text-black">1,247</p>
                                <p class="text-xs text-gray-500">总SKU数</p>
                            </div>
                            <div>
                                <p class="text-lg font-bold text-black">12</p>
                                <p class="text-xs text-gray-500">目标池位</p>
                            </div>
                            <div>
                                <p class="text-lg font-bold text-black">85,432</p>
                                <p class="text-xs text-gray-500">总库存件数</p>
                            </div>
                            <div>
                                <p class="text-lg font-bold text-black">78,901</p>
                                <p class="text-xs text-gray-500">总需求件数</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Distribution Chart -->
                <div class="bg-white rounded-2xl p-6 card-shadow">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-black">库存分布分析</h3>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg text-sm font-medium">固口类</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-lg text-sm">非固口类</button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Pool Distribution -->
                        <div>
                            <h4 class="text-sm font-medium text-black mb-4">各池位库存状况</h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">华东仓</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-black">8,542</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">华南仓</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: 72%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-black">7,234</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">华北仓</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 45%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-black">4,521</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">西南仓</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-red-500 h-2 rounded-full" style="width: 28%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-black">2,801</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category Distribution -->
                        <div>
                            <h4 class="text-sm font-medium text-black mb-4">产品类别分布</h4>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">手镯类</span>
                                    </div>
                                    <span class="text-sm font-medium text-black">35.2%</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">戒指类</span>
                                    </div>
                                    <span class="text-sm font-medium text-black">28.7%</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-purple-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">项链类</span>
                                    </div>
                                    <span class="text-sm font-medium text-black">22.1%</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-orange-500 rounded-full"></div>
                                        <span class="text-sm text-gray-600">其他类</span>
                                    </div>
                                    <span class="text-sm font-medium text-black">14.0%</span>
                                </div>
                            </div>

                            <div class="mt-6 p-4 bg-blue-50 rounded-xl">
                                <div class="flex items-center space-x-2 mb-2">
                                    <i class="fas fa-lightbulb text-blue-500"></i>
                                    <span class="text-sm font-medium text-black">智能建议</span>
                                </div>
                                <p class="text-xs text-gray-600">
                                    西南仓库存严重不足，建议优先从华东仓调拨手镯类产品156件，预计可提升该区域销售效率23%
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                        <!-- Right Column -->
                        <div class="space-y-5">
                <!-- Algorithm Status -->
                <div class="premium-card rounded-2xl p-5 card-shadow fade-in-scale">
                    <h3 class="text-lg font-bold text-black mb-4 sf-pro-display">算法状态</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-2 rounded-lg hover:bg-white/30 transition-all duration-200">
                            <span class="text-sm text-gray-600 font-medium">数据预处理</span>
                            <span class="text-green-500"><i class="fas fa-check-circle"></i></span>
                        </div>
                        <div class="flex items-center justify-between p-2 rounded-lg hover:bg-white/30 transition-all duration-200">
                            <span class="text-sm text-gray-600 font-medium">权重计算</span>
                            <span class="text-green-500"><i class="fas fa-check-circle"></i></span>
                        </div>
                        <div class="flex items-center justify-between p-2 rounded-lg bg-blue-50/50 border border-blue-100">
                            <span class="text-sm text-gray-600 font-medium">智能分配</span>
                            <span class="text-blue-500 pulse-animation"><i class="fas fa-spinner fa-spin"></i></span>
                        </div>
                        <div class="flex items-center justify-between p-2 rounded-lg hover:bg-white/30 transition-all duration-200">
                            <span class="text-sm text-gray-600 font-medium">结果优化</span>
                            <span class="text-gray-400"><i class="fas fa-clock"></i></span>
                        </div>
                    </div>

                    <div class="mt-5">
                        <div class="flex justify-between text-sm text-gray-600 mb-2 font-medium">
                            <span>处理进度</span>
                            <span class="font-bold">67%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                            <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-1000" style="width: 67%"></div>
                        </div>
                    </div>
                </div>

                <!-- Recent Results -->
                <div class="premium-card rounded-2xl p-5 card-shadow fade-in-scale">
                    <h3 class="text-lg font-bold text-black mb-4 sf-pro-display">最近调拨结果</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100 hover:shadow-md transition-all duration-300">
                            <div>
                                <p class="text-sm font-bold text-black">华东仓 → 华南仓</p>
                                <p class="text-xs text-gray-500 mt-0.5">手镯类 · 156件</p>
                            </div>
                            <div class="flex items-center space-x-1">
                                <div class="w-1.5 h-1.5 bg-green-400 rounded-full"></div>
                                <span class="text-green-600 text-xs font-bold">完成</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100 hover:shadow-md transition-all duration-300">
                            <div>
                                <p class="text-sm font-bold text-black">华北仓 → 西南仓</p>
                                <p class="text-xs text-gray-500 mt-0.5">戒指类 · 89件</p>
                            </div>
                            <div class="flex items-center space-x-1">
                                <div class="w-1.5 h-1.5 bg-blue-400 rounded-full pulse-animation"></div>
                                <span class="text-blue-600 text-xs font-bold">进行中</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl border border-orange-100 hover:shadow-md transition-all duration-300">
                            <div>
                                <p class="text-sm font-bold text-black">华中仓 → 华东仓</p>
                                <p class="text-xs text-gray-500 mt-0.5">项链类 · 234件</p>
                            </div>
                            <div class="flex items-center space-x-1">
                                <div class="w-1.5 h-1.5 bg-orange-400 rounded-full"></div>
                                <span class="text-orange-600 text-xs font-bold">待确认</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="premium-card rounded-2xl p-5 card-shadow fade-in-scale">
                    <h3 class="text-lg font-bold text-black mb-4 sf-pro-display">快速操作</h3>
                    <div class="space-y-2">
                        <button class="w-full text-left p-3 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 rounded-xl transition-all duration-300 hover:shadow-md group">
                            <div class="flex items-center">
                                <i class="fas fa-file-download text-blue-500 mr-3 group-hover:scale-110 transition-transform"></i>
                                <span class="text-sm font-bold text-black">下载调拨报告</span>
                            </div>
                        </button>
                        <button class="w-full text-left p-3 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 rounded-xl transition-all duration-300 hover:shadow-md group">
                            <div class="flex items-center">
                                <i class="fas fa-chart-bar text-green-500 mr-3 group-hover:scale-110 transition-transform"></i>
                                <span class="text-sm font-bold text-black">查看分析报表</span>
                            </div>
                        </button>
                        <button class="w-full text-left p-3 hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 rounded-xl transition-all duration-300 hover:shadow-md group">
                            <div class="flex items-center">
                                <i class="fas fa-cog text-purple-500 mr-3 group-hover:scale-110 transition-transform"></i>
                                <span class="text-sm font-bold text-black">算法参数设置</span>
                            </div>
                        </button>
                    </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing Algorithm Section -->
                <div id="pricing-section" class="section-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <div class="lg:col-span-2">
                            <div class="glass-effect rounded-3xl p-6 card-shadow">
                                <h3 class="text-lg font-semibold text-black mb-4 sf-pro-display">智能定价控制台</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div>
                                        <label class="block text-sm font-medium text-black mb-2">定价策略</label>
                                        <select class="apple-input w-full p-3 text-sm">
                                            <option>竞争导向定价</option>
                                            <option>成本加成定价</option>
                                            <option>价值导向定价</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-black mb-2">价格弹性系数</label>
                                        <input type="number" value="1.2" step="0.1" class="apple-input w-full p-3 text-sm">
                                    </div>
                                </div>
                                <button class="apple-button px-6 py-3 text-white font-medium">
                                    <i class="fas fa-calculator mr-2"></i>
                                    开始智能定价
                                </button>
                            </div>
                        </div>
                        <div>
                            <div class="glass-effect rounded-3xl p-6 card-shadow">
                                <h3 class="text-lg font-semibold text-black mb-4 sf-pro-display">定价状态</h3>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">处理商品</span>
                                        <span class="text-sm font-semibold text-black">2,341</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">价格优化率</span>
                                        <span class="text-sm font-semibold text-green-600">87.3%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Price Control Section -->
                <div id="pricecontrol-section" class="section-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="glass-effect rounded-3xl p-6 card-shadow">
                            <h3 class="text-lg font-semibold text-black mb-4 sf-pro-display">价格监控</h3>
                            <div class="h-64 bg-gray-50 rounded-2xl flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-chart-line text-4xl text-gray-300 mb-2"></i>
                                    <p class="text-gray-500">价格趋势图表</p>
                                </div>
                            </div>
                        </div>
                        <div class="glass-effect rounded-3xl p-6 card-shadow">
                            <h3 class="text-lg font-semibold text-black mb-4 sf-pro-display">控价规则</h3>
                            <div class="space-y-4">
                                <div class="p-4 bg-blue-50 rounded-2xl">
                                    <h4 class="font-medium text-black mb-2">最低价格保护</h4>
                                    <p class="text-sm text-gray-600">确保价格不低于成本的120%</p>
                                </div>
                                <div class="p-4 bg-green-50 rounded-2xl">
                                    <h4 class="font-medium text-black mb-2">竞争价格跟踪</h4>
                                    <p class="text-sm text-gray-600">实时监控竞品价格变化</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product Monitoring Section -->
                <div id="monitoring-section" class="section-content hidden">
                    <div class="glass-effect rounded-3xl p-6 card-shadow mb-6">
                        <h3 class="text-lg font-semibold text-black mb-4 sf-pro-display">商品监控中心</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl">
                                <h4 class="font-semibold text-black mb-2">库存预警</h4>
                                <p class="text-2xl font-bold text-blue-600">23</p>
                                <p class="text-sm text-gray-600">商品库存不足</p>
                            </div>
                            <div class="p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl">
                                <h4 class="font-semibold text-black mb-2">价格异常</h4>
                                <p class="text-2xl font-bold text-orange-600">7</p>
                                <p class="text-sm text-gray-600">价格波动异常</p>
                            </div>
                            <div class="p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl">
                                <h4 class="font-semibold text-black mb-2">销量突增</h4>
                                <p class="text-2xl font-bold text-green-600">156</p>
                                <p class="text-sm text-gray-600">销量显著增长</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Section -->
                <div id="analytics-section" class="section-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <div class="glass-effect rounded-3xl p-6 card-shadow">
                            <h3 class="text-lg font-semibold text-black mb-4 sf-pro-display">调拨效率分析</h3>
                            <div class="h-64 bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-chart-line text-4xl text-blue-400 mb-2"></i>
                                    <p class="text-gray-600 font-medium">效率趋势图表</p>
                                </div>
                            </div>
                        </div>
                        <div class="glass-effect rounded-3xl p-6 card-shadow">
                            <h3 class="text-lg font-semibold text-black mb-4 sf-pro-display">成本节省分析</h3>
                            <div class="h-64 bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-coins text-4xl text-green-400 mb-2"></i>
                                    <p class="text-gray-600 font-medium">成本分析图表</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Settings Section -->
                <div id="aisettings-section" class="section-content hidden">
                    <div class="max-w-4xl space-y-6">
                        <div class="glass-effect rounded-3xl p-6 card-shadow">
                            <h3 class="text-lg font-semibold text-black mb-4 sf-pro-display">AI算法参数配置</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-black mb-2">默认主权重系数</label>
                                    <input type="number" value="0.75" step="0.01" class="apple-input w-full p-3 text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-black mb-2">默认二级权重系数</label>
                                    <input type="number" value="0.25" step="0.01" class="apple-input w-full p-3 text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-black mb-2">学习率</label>
                                    <input type="number" value="0.001" step="0.0001" class="apple-input w-full p-3 text-sm">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-black mb-2">模型更新频率</label>
                                    <select class="apple-input w-full p-3 text-sm">
                                        <option>每日更新</option>
                                        <option>每周更新</option>
                                        <option>每月更新</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="glass-effect rounded-3xl p-6 card-shadow">
                            <h3 class="text-lg font-semibold text-black mb-4 sf-pro-display">AI功能开关</h3>
                            <div class="space-y-6">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-sm font-semibold text-black">智能调拨</span>
                                        <p class="text-xs text-gray-500">自动执行库存调拨决策</p>
                                    </div>
                                    <button class="w-14 h-7 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full relative shadow-sm">
                                        <div class="w-6 h-6 bg-white rounded-full absolute right-0.5 top-0.5 shadow-sm"></div>
                                    </button>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-sm font-semibold text-black">智能定价</span>
                                        <p class="text-xs text-gray-500">基于市场数据自动调整价格</p>
                                    </div>
                                    <button class="w-14 h-7 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full relative shadow-sm">
                                        <div class="w-6 h-6 bg-white rounded-full absolute right-0.5 top-0.5 shadow-sm"></div>
                                    </button>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-sm font-semibold text-black">商品监控</span>
                                        <p class="text-xs text-gray-500">实时监控商品状态异常</p>
                                    </div>
                                    <button class="w-14 h-7 bg-gray-300 rounded-full relative shadow-sm">
                                        <div class="w-6 h-6 bg-white rounded-full absolute left-0.5 top-0.5 shadow-sm"></div>
                                    </button>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-sm font-semibold text-black">邮件通知</span>
                                        <p class="text-xs text-gray-500">重要事件邮件提醒</p>
                                    </div>
                                    <button class="w-14 h-7 bg-gray-300 rounded-full relative shadow-sm">
                                        <div class="w-6 h-6 bg-white rounded-full absolute left-0.5 top-0.5 shadow-sm"></div>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="glass-effect rounded-3xl p-6 card-shadow">
                            <h3 class="text-lg font-semibold text-black mb-4 sf-pro-display">模型性能监控</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl">
                                    <h4 class="font-semibold text-black mb-2">准确率</h4>
                                    <p class="text-2xl font-bold text-green-600">94.2%</p>
                                    <p class="text-sm text-gray-600">模型预测准确率</p>
                                </div>
                                <div class="p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl">
                                    <h4 class="font-semibold text-black mb-2">响应时间</h4>
                                    <p class="text-2xl font-bold text-blue-600">1.2s</p>
                                    <p class="text-sm text-gray-600">平均处理时间</p>
                                </div>
                                <div class="p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl">
                                    <h4 class="font-semibold text-black mb-2">处理量</h4>
                                    <p class="text-2xl font-bold text-purple-600">2.3万</p>
                                    <p class="text-sm text-gray-600">日处理商品数</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Algorithm Details Modal (Hidden by default) -->
    <div id="algorithmModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-2xl p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-black">智能调拨算法详情</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-black">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Algorithm Flow -->
                <div>
                    <h4 class="text-lg font-semibold text-black mb-4">算法流程</h4>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                            <div>
                                <h5 class="font-medium text-black">产品分类识别</h5>
                                <p class="text-sm text-gray-600">自动识别固口类与非固口类产品，应用不同处理策略</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                            <div>
                                <h5 class="font-medium text-black">库存状况评估</h5>
                                <p class="text-sm text-gray-600">分析总库存与总需求，判断充足或不足情况</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                            <div>
                                <h5 class="font-medium text-black">智能权重计算</h5>
                                <p class="text-sm text-gray-600">主权重优先库存少的池位，二级权重平衡SKU分配</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                            <div>
                                <h5 class="font-medium text-black">随机化分配</h5>
                                <p class="text-sm text-gray-600">防止SKU偏向性操控，确保分配公平性</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center text-sm font-bold">5</div>
                            <div>
                                <h5 class="font-medium text-black">结果优化输出</h5>
                                <p class="text-sm text-gray-600">生成各池位独立分配文件，支持Excel导出</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Algorithm Parameters -->
                <div>
                    <h4 class="text-lg font-semibold text-black mb-4">核心参数</h4>
                    <div class="space-y-4">
                        <div class="bg-gray-50 rounded-xl p-4">
                            <h5 class="font-medium text-black mb-2">主权重系数 (α)</h5>
                            <p class="text-sm text-gray-600 mb-3">控制库存均衡的优先级，值越大越倾向于给库存少的池位分配更多商品</p>
                            <div class="flex items-center space-x-3">
                                <span class="text-sm text-gray-500">0.5</span>
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                                <span class="text-sm text-gray-500">1.0</span>
                                <span class="text-sm font-medium text-black">0.75</span>
                            </div>
                        </div>

                        <div class="bg-gray-50 rounded-xl p-4">
                            <h5 class="font-medium text-black mb-2">二级权重系数 (β)</h5>
                            <p class="text-sm text-gray-600 mb-3">平衡SKU分配效率，确保库存多的SKU能够优先分配</p>
                            <div class="flex items-center space-x-3">
                                <span class="text-sm text-gray-500">0.1</span>
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 50%"></div>
                                </div>
                                <span class="text-sm text-gray-500">0.5</span>
                                <span class="text-sm font-medium text-black">0.25</span>
                            </div>
                        </div>

                        <div class="bg-gray-50 rounded-xl p-4">
                            <h5 class="font-medium text-black mb-2">随机化因子 (γ)</h5>
                            <p class="text-sm text-gray-600 mb-3">防止算法偏向性，增加分配结果的随机性和公平性</p>
                            <div class="flex items-center space-x-3">
                                <span class="text-sm text-gray-500">0.0</span>
                                <div class="flex-1 bg-gray-200 rounded-full h-2">
                                    <div class="bg-purple-500 h-2 rounded-full" style="width: 30%"></div>
                                </div>
                                <span class="text-sm text-gray-500">0.3</span>
                                <span class="text-sm font-medium text-black">0.1</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h5 class="font-medium text-black mb-3">算法优势</h5>
                        <div class="space-y-2">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500"></i>
                                <span class="text-sm text-gray-600">随机性防止SKU偏向操控</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500"></i>
                                <span class="text-sm text-gray-600">可控性确保不超总库存</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500"></i>
                                <span class="text-sm text-gray-600">灵活输出支持多格式导出</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-500"></i>
                                <span class="text-sm text-gray-600">参数可调适应不同业务</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-8 flex justify-end space-x-4">
                <button onclick="closeModal()" class="px-6 py-2 border border-gray-200 text-black rounded-xl hover:bg-gray-50">
                    关闭
                </button>
                <button class="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:shadow-lg">
                    应用设置
                </button>
            </div>
        </div>
    </div>

    <script>
        function openModal() {
            document.getElementById('algorithmModal').classList.remove('hidden');
            document.getElementById('algorithmModal').classList.add('flex');
        }

        function closeModal() {
            document.getElementById('algorithmModal').classList.add('hidden');
            document.getElementById('algorithmModal').classList.remove('flex');
        }

        function showSection(sectionName) {
            // Hide all sections
            const sections = document.querySelectorAll('.section-content');
            sections.forEach(section => {
                section.classList.add('hidden');
            });

            // Remove active class from all nav items
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.classList.remove('active', 'bg-blue-50', 'text-blue-600');
                item.classList.add('text-gray-600');
            });

            // Show selected section
            const targetSection = document.getElementById(sectionName + '-section');
            if (targetSection) {
                targetSection.classList.remove('hidden');
            }

            // Add active class to clicked nav item
            event.target.closest('.nav-item').classList.add('active', 'bg-blue-50', 'text-blue-600');
            event.target.closest('.nav-item').classList.remove('text-gray-600');

            // Update page title and description
            const titles = {
                'dashboard': { title: '仪表盘', desc: '全面掌握智能商品管理系统运行状况' },
                'allocation': { title: '智能库存调拨', desc: '基于AI算法的智能库存分配，解决多仓库间库存不均衡问题' },
                'pricing': { title: '定价算法', desc: '智能定价策略，基于市场数据和竞争分析优化商品价格' },
                'pricecontrol': { title: '智能控价', desc: '实时价格监控与自动调整，确保价格策略执行到位' },
                'monitoring': { title: '商品监控', desc: '全方位商品状态监控，及时发现库存、价格、销量异常' },
                'analytics': { title: '数据分析', desc: '深度分析业务数据，洞察运营效率与优化机会' },
                'aisettings': { title: 'AI设置', desc: '配置AI算法参数，管理智能功能开关与模型性能' }
            };

            if (titles[sectionName]) {
                document.getElementById('pageTitle').textContent = titles[sectionName].title;
                document.getElementById('pageDescription').textContent = titles[sectionName].desc;
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Show allocation section by default
            showSection('allocation');

            // Add modal trigger to algorithm settings button
            const settingsButtons = document.querySelectorAll('button');
            settingsButtons.forEach(button => {
                if (button.textContent.includes('算法参数设置')) {
                    button.addEventListener('click', openModal);
                }
            });

            // Simulate real-time updates
            setInterval(() => {
                const progressBar = document.querySelector('.bg-gradient-to-r.from-blue-500.to-purple-600');
                if (progressBar) {
                    const currentWidth = parseInt(progressBar.style.width) || 67;
                    const newWidth = Math.min(currentWidth + Math.random() * 2, 100);
                    progressBar.style.width = newWidth + '%';

                    const progressText = document.querySelector('.text-sm.text-gray-600 + span');
                    if (progressText) {
                        progressText.textContent = Math.round(newWidth) + '%';
                    }
                }
            }, 3000);
        });
    </script>
</body>
</html>
